.hero {
  background: var(--bg-primary);
  min-height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 100px 0 60px 0; /* Account for fixed header */
}

.hero-content {
  position: relative;
  text-align: center;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 24px;
  width: 100%;
  max-width: 919px;
  margin: 0 auto;
}

.hero-headline-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  gap: 12px;
  isolation: isolate;
  width: 100%;
  max-width: 919px;
  flex: none;
  order: 0;
  align-self: stretch;
  flex-grow: 0;
}

.hero-badge {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 10px;
  width: 205px;
  height: 37px;
  border-radius: 999px;
  flex: none;
  order: 0;
  flex-grow: 0;
  z-index: 0;
  background: transparent;
  border: thin solid #d8c975;
}

.hero-badge-text {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  background: linear-gradient(267.18deg, #d19049 -33.36%, #BF4129 126.44%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding: 10px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.hero-title {
  width: 100%;
  max-width: 919px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 48px;
  line-height: 150%;
  text-align: center;
  color: #CC6754;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  z-index: 1;
  margin: 0;
}

.hero-desc {
  width: 100%;
  max-width: 627px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  text-align: center;
  color: #D4D4D4;
  flex: none;
  order: 2;
  flex-grow: 0;
  z-index: 2;
  margin: 0;
}

.hero-vector {
  position: absolute;
  width: 300px;
  height: 13px;
  left: 451px;
  top: 184px;
  background: #C6741B;
  flex: none;
  order: 3;
  flex-grow: 0;
  z-index: 3;
}

.hero-element04 {
  position: absolute;
  width: 176px;
  height: 71.65px;
  left: 146px;
  top: 72px;
  flex: none;
  order: 4;
  flex-grow: 0;
  z-index: 4;
}

.hero-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 16px;
  gap: 12px;
  width: 238px;
  height: 56px;
  background: #BF4129;
  border-radius: 999px;
  flex: none;
  order: 1;
  flex-grow: 0;
  border: none;
  cursor: pointer;
}

.hero-btn-label {
  width: 170px;
  height: 22px;
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #FAFAFA;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.hero-btn-icon {
  width: 24px;
  height: 24px;
  flex: none;
  order: 2;
  flex-grow: 0;
}

/* Floating Icons */
.floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
}

/* Left side icons */
.icon-left-1 {
  position: absolute;
  width: 190px;
  height: 190px;
  top: 35%;
  left: -5%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 0s;
}

.icon-left-2 {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 80%;
  left: 20%;
  animation: floatReverse 8s ease-in-out infinite;
  animation-delay: 2s;
}

/* Right side icons */
.icon-right-1 {
  position: absolute;
  width: 115px;
  height: 115px;
  top: 78%;
  right: 17%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 4s;
}

.icon-right-3 {
  position: absolute;
  width: 140px;
  height: 140px;
  top: 39% ;
  right: -3%;
  animation: floatSlow 10s ease-in-out infinite;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
  75% {
    transform: translateY(-15px) rotate(270deg);
  }
}

/* Animation keyframes remain the same */

@keyframes floatReverse {
  0%, 100% {
    transform: translateY(0px) rotate(360deg);
  }
  50% {
    transform: translateY(-25px) rotate(180deg);
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.1);
  }
}

/* Remove duplicate styles - using Figma-based styles above */

/* Responsive */
@media (max-width: 768px) {
  
  .hero {
    margin-top: -90px;
  }
  .hero-title {
    font-size: 40px;
  }

  .hero-subtitle {
    font-size: 28px;
  }

  .subtitle-confidence {
    padding: 6px 12px;
    font-size: 24px;
  }

  .hero-description {
    font-size: 16px;
    margin-bottom: 32px;
  }

  .icon-left-1 {
    width: 120px;
    height: 120px;
    top: 60%;
  }

  .icon-left-2 {
    width: 65px;
    height: 65px;
    top:102%;
    left: 15%;
  }

  .icon-right-1 {
    width: 80px;
    height: 80px;
    top: 100%;
    right: 15%;
  }

  .icon-right-3 {
    width: 100px;
    height: 100px;
    top: 60%;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 24px;
  }

  .subtitle-confidence {
    font-size: 20px;
    padding: 4px 8px;
  }

  .floating-icons {
    opacity: 0.7;
  }
}

/* Remove duplicate styles - already defined above */

/* Responsive adjustments */
@media (max-width: 1000px) {
  

  .hero-content {
    width: 95vw;
    max-width: 919px;
  }

  .hero-headline-group {
    width: 100%;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-desc {
    font-size: 15px;
    max-width: 90%;
  }

  .hero-btn {
    width: 280px;
  }
}

@media (max-width: 768px) {
  

  .hero-content {
    width: 100%;
    gap: 20px;
  }

  .hero-headline-group {
    gap: 10px;
  }

  .hero-title {
    font-size: 28px;
    line-height: 1.3;
  }

  .hero-desc {
    font-size: 14px;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
    height: 48px;
  }

  .hero-btn-label {
    font-size: 16px;
  }
}

@media (max-width: 480px) {

  .hero-content {
    gap: 16px;
  }

  .hero-headline-group {
    gap: 8px;
  }

  .hero-title {
    font-size: 24px;
  }

  .hero-badge {
    width: auto;
    padding: 8px 12px;
  }

  .hero-badge-text {
    font-size: 12px;
  }
}
