import React, { useState, useRef, useEffect } from 'react';
import '../styles/components/TreasuryDropdown.css';

// Import icons
import chevronDownIcon from '../assets/arrow-down.png';

interface DropdownOption {
  id: string;
  label: string;
}

interface TreasuryDropdownProps {
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const TreasuryDropdown: React.FC<TreasuryDropdownProps> = ({
  options,
  selectedValue,
  onSelect,
  placeholder = "Select option",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<DropdownOption | null>(
    options.find(option => option.id === selectedValue) || null
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected option when selectedValue prop changes
  useEffect(() => {
    const option = options.find(option => option.id === selectedValue);
    setSelectedOption(option || null);
  }, [selectedValue, options]);

  // Handle option selection
  const handleOptionSelect = (option: DropdownOption) => {
    setSelectedOption(option);
    setIsOpen(false);
    onSelect(option.id);
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div 
      className={`treasury-dropdown-container ${className} ${isOpen ? 'open' : ''}`}
      ref={dropdownRef}
    >
      {/* Dropdown Trigger */}
      <button
        className="treasury-dropdown-trigger"
        onClick={toggleDropdown}
        onKeyDown={handleKeyDown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        type="button"
      >
        <span className="dropdown-text">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <div className={`dropdown-arrow ${isOpen ? 'open' : ''}`}>
          <img src={chevronDownIcon} alt="Toggle" />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="treasury-dropdown-menu" role="listbox">
          {options.map((option) => (
            <button
              key={option.id}
              className={`treasury-dropdown-option ${selectedOption?.id === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option)}
              role="option"
              aria-selected={selectedOption?.id === option.id}
              type="button"
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default TreasuryDropdown;
