import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Hero.css';
import icon1 from '../assets/icon 1.png';
import icon2 from '../assets/home-gold-white.png';
import icon3 from '../assets/home-gold.png';

const Hero: React.FC = () => {
  const navigate = useNavigate();

  const handleStartStaking = () => {
    // Navigate to My Stake page in user dashboard
    navigate('/user-dashboard/my-stake');
  };

  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          {/* Floating Icons */}
          <div className="floating-icons">
            {/* Left side icons */}
            <div className="icon-left-1">
              <img src={icon1} alt="Icon 1" className="hero-icon" />
            </div>
            <div className="icon-left-2">
              <img src={icon2} alt="Icon 2" className="hero-icon" />
            </div>

            {/* Right side icons */}
            <div className="icon-right-1">
              <img src={icon1} alt="Icon 1" className="hero-icon" />
            </div>
            <div className="icon-right-3">
              <img src={icon3} alt="Icon 3" className="hero-icon" />
            </div>
          </div>

          {/* Hero Headline Group */}
          <div className="hero-headline-group">
            {/* Earn Badge */}
            <div className="hero-badge">
              <span className="hero-badge-text">Earn 20% annual returns</span>
            </div>

            {/* Main Title */}
            <h1 className="hero-title">
              Grow Your Wealth. Fund the Future.
            </h1>

            {/* Description */}
            <p className="hero-desc">
              Stake your ESVC tokens securely, earn daily ROI, and unlock exclusive
              opportunities to pitch your startup ideas for funding. Transparent treasury
              tracking, secure crypto deposits, and tiered rewards.
            </p>
          </div>

          {/* CTA Button */}
          <button className="hero-btn" onClick={handleStartStaking}>
            <span className="hero-btn-label">Start Staking Now</span>
            <div className="hero-btn-icon">🚀</div>
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
