import React, { useState, useRef, useEffect } from 'react';
import '../../styles/components/modals/CurrencyDropdown.css';

// Import icons
import chevronDownIcon from '../../assets/arrow-down.png';

interface CurrencyOption {
  id: string;
  name: string;
  symbol: string;
  icon: string;
  network?: string;
}

interface CurrencyDropdownProps {
  options: CurrencyOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const CurrencyDropdown: React.FC<CurrencyDropdownProps> = ({
  options,
  selectedValue,
  onSelect,
  placeholder = "Click to select",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<CurrencyOption | null>(
    options.find(option => option.id === selectedValue) || null
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected option when selectedValue prop changes
  useEffect(() => {
    const option = options.find(option => option.id === selectedValue);
    setSelectedOption(option || null);
  }, [selectedValue, options]);

  // Handle option selection
  const handleOptionSelect = (option: CurrencyOption) => {
    setSelectedOption(option);
    setIsOpen(false);
    onSelect(option.id);
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div 
      className={`currency-dropdown-container ${className} ${isOpen ? 'open' : ''}`}
      ref={dropdownRef}
    >
      {/* Dropdown Trigger */}
      <button
        className="currency-dropdown-trigger"
        onClick={toggleDropdown}
        onKeyDown={handleKeyDown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        type="button"
      >
        <div className="trigger-content">
          {selectedOption ? (
            <>
              <div className="currency-icon">
                <img src={selectedOption.icon} alt={selectedOption.symbol} />
              </div>
              <div className="currency-info">
                <span className="currency-name">{selectedOption.name}</span>
              </div>
            </>
          ) : (
            <div className="currency-placeholder">
              <div className="placeholder-icon">💰</div>
              <span className="placeholder-text">{placeholder}</span>
            </div>
          )}
        </div>
        <div className={`dropdown-arrow ${isOpen ? 'open' : ''}`}>
          <img src={chevronDownIcon} alt="Toggle" />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="currency-dropdown-menu" role="listbox">
          {options.map((option) => (
            <button
              key={option.id}
              className={`currency-dropdown-option ${selectedOption?.id === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option)}
              role="option"
              aria-selected={selectedOption?.id === option.id}
              type="button"
            >
              <div className="option-icon">
                <img src={option.icon} alt={option.symbol} />
              </div>
              <div className="option-content">
                <div className="option-main">
                  <span className="option-name">{option.name}</span>
                  <span className="option-symbol">({option.symbol})</span>
                </div>
                {option.network && (
                  <span className="option-network">{option.network} network</span>
                )}
              </div>
              {selectedOption?.id === option.id && (
                <div className="option-check">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path 
                      d="M13.5 4.5L6 12L2.5 8.5" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CurrencyDropdown;
