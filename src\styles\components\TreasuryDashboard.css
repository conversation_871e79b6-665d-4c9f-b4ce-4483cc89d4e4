.treasury-dashboard {
  background: linear-gradient(135deg, var(--bg-primary) 0%, #1f1f1f 50%, var(--bg-primary) 100%);
  padding: 80px 0;
  position: relative;
}

.treasury-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(232, 90, 79, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 50%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 60px;
}

.dashboard-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.dashboard-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 60px;
}

.stat-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-height: 160px;
  width: 120% !; /* Ensure consistent width */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  font-size: 14px;
  opacity: 0.7;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: #4ade80;
}

.stat-change.neutral {
  color: var(--text-secondary);
}

.change-icon {
  font-size: 12px;
}

/* Holdings Grid */
.holdings-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 60px;
}

.holding-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-height: 160px;
}

.holding-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.holding-card:hover .holding-icon {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.holding-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.holding-icon.btc {
  background: linear-gradient(135deg, #f7931a, #ffb347);
}

.holding-icon.sol {
  background: linear-gradient(135deg, #9945ff, #14f195);
}

.holding-icon.usdc {
  background: linear-gradient(135deg, #2775ca, #4dabf7);
}

.holding-icon.esvc {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
}

.holding-info {
  flex: 1;
}

.holding-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 4px;
}

.holding-amount {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.currency {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.holding-change {
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Dashboard Actions */
.dashboard-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.dashboard-actions .btn-primary,
.dashboard-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

.arrow {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.btn-secondary:hover .arrow {
  transform: translateX(4px);
}

/* Desktop Responsive */
@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .holdings-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr); /* 2x2 grid on tablets */
    gap: 20px;
  }

  .stat-card {
    min-height: 150px; /* Consistent height on tablets */
  }

  .holdings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .treasury-dashboard {
    padding: 60px 0 !important;
  }

  .dashboard-header {
    margin-bottom: 40px !important;
    padding: 0 20px !important;
  }

  .dashboard-title {
    font-size: 32px !important;
    margin-bottom: 16px !important;
    line-height: 1.2 !important;
  }

  .dashboard-subtitle {
    font-size: 16px !important;
    line-height: 1.5 !important;
    margin-bottom: 0 !important;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px !important;
    margin-bottom: 40px !important;
    padding: 0 20px !important;
    margin-left: 20px !important; /* Shift right to align with title */
  }

  .holdings-grid {
    grid-template-columns: 1fr;
    gap: 16px !important;
    margin-bottom: 40px !important;
    padding: 0 20px !important; /* Same padding as stats grid */
    margin-left: 20px !important; /* Shift right to align with title */
  }

  .stat-card {
    padding: 20px !important;
    border-radius: 16px !important;
    background: rgba(38, 38, 38, 0.6) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-height: 140px !important;
    width: 100% !important; /* Ensure full width on mobile */
    box-sizing: border-box !important; /* Include padding in width calculation */
  }

  .holding-card {
    padding: 16px !important;
    border-radius: 16px !important;
    background: rgba(38, 38, 38, 0.6) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    min-height: 140px !important;
    width: 100% !important; /* Ensure full width within grid */
    box-sizing: border-box !important; /* Include padding in width calculation */
  }

  .stat-label {
    font-size: 11px !important;
    margin-bottom: 8px !important;
    color: var(--text-secondary) !important;
  }

  .stat-value {
    font-size: 28px !important;
    margin-bottom: 8px !important;
    color: var(--text-primary) !important;
    font-weight: 700 !important;
  }

  .stat-change {
    font-size: 13px !important;
    color: #4ade80 !important;
  }

  .holding-amount {
    font-size: 18px !important;
    margin-bottom: 4px !important;
    color: var(--text-primary) !important;
    font-weight: 700 !important;
  }

  .holding-label {
    font-size: 11px !important;
    margin-bottom: 4px !important;
    color: var(--text-secondary) !important;
  }

  .holding-change {
    font-size: 12px !important;
    color: #4ade80 !important;
  }

  .currency {
    font-size: 14px !important;
    color: var(--text-secondary) !important;
  }

  .dashboard-actions {
    flex-direction: column;
    align-items: center;
    padding: 0 20px !important;
    gap: 16px !important;
    margin-top: 20px !important;
  }

  .dashboard-actions .btn-primary,
  .dashboard-actions .btn-secondary {
    width: 100%;
    max-width: 320px !important;
    justify-content: center;
    padding: 16px 24px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }

  /* Additional mobile optimizations */
  .holding-icon {
    width: 40px !important;
    height: 40px !important;
    font-size: 20px !important;
    flex-shrink: 0 !important;
  }

  .holding-card {
    gap: 16px !important;
    align-items: center !important;
  }

  .holding-info {
    flex: 1 !important;
    min-width: 0 !important;
  }

  /* Ensure proper container spacing */
  .container {
    padding: 0 !important;
    max-width: 100% !important;
  }

  /* Prevent content overflow */
  .treasury-dashboard {
    overflow-x: hidden !important;
  }

  /* Info icon spacing */
  .info-icon {
    margin-left: 8px !important;
  }
}
