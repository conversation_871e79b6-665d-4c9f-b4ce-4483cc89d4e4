/* Universal Mobile Dropdown Fixes */

/* Base dropdown styles for mobile optimization */
@media (max-width: 768px) {
  
  /* All select elements */
  select {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 12px 16px !important;
    border-radius: 8px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    min-height: 44px !important; /* Apple's recommended touch target */
    background-color: #262626 !important;
    border: 2px solid #404040 !important;
    color: #FFFFFF !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 16px !important;
    padding-right: 44px !important;
  }

  select:focus {
    border-color: #BF4129 !important;
    box-shadow: 0 0 0 3px rgba(191, 65, 41, 0.2) !important;
    outline: none !important;
  }

  /* Currency Dropdown Mobile Fixes */
  .currency-dropdown-trigger {
    padding: 14px 18px !important;
    border-radius: 10px !important;
    font-size: 15px !important;
    min-height: 48px !important;
    margin-bottom: 16px !important;
  }

  .currency-dropdown-menu {
    border-radius: 0 0 10px 10px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
  }

  .currency-dropdown-option {
    padding: 14px 18px !important;
    font-size: 15px !important;
  }

  .currency-icon {
    width: 24px !important;
    height: 24px !important;
    padding: 3px !important;
  }

  .currency-icon img {
    width: 16px !important;
    height: 16px !important;
  }

  .option-icon {
    width: 24px !important;
    height: 24px !important;
    padding: 3px !important;
  }

  .option-icon img {
    width: 16px !important;
    height: 16px !important;
  }

  /* Wallet Dropdown Mobile Fixes */
  .wallet-dropdown-trigger {
    padding: 14px 18px !important;
    border-radius: 10px !important;
    font-size: 15px !important;
    min-height: 48px !important;
  }

  .wallet-dropdown-menu {
    border-radius: 0 0 10px 10px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
  }

  .wallet-dropdown-option {
    padding: 14px 18px !important;
    font-size: 15px !important;
  }

  .trigger-icon {
    width: 20px !important;
    height: 20px !important;
    padding: 3px !important;
  }

  .trigger-icon img {
    width: 14px !important;
    height: 14px !important;
  }

  /* Dashboard Mode Selector Mobile Fixes */
  .dropdown-trigger {
    padding: 12px 16px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    min-height: 44px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .dropdown-menu {
    border-radius: 0 0 8px 8px !important;
    max-height: 50vh !important;
    overflow-y: auto !important;
    width: 100% !important;
  }

  .dropdown-item {
    padding: 12px 16px !important;
    font-size: 14px !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
  }

  /* Form Select Elements */
  .form-select {
    font-size: 16px !important;
    padding: 14px 16px !important;
    padding-right: 44px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    background-size: 16px !important;
    background-position: right 14px center !important;
  }

  /* Get Funding Form Dropdowns */
  .pitch-form-container select {
    font-size: 16px !important;
    padding: 14px 16px !important;
    padding-right: 44px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Trading Dashboard Dropdowns */
  .trading-form select {
    font-size: 16px !important;
    padding: 14px 16px !important;
    padding-right: 44px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Modal Dropdowns */
  .modal select,
  .stake-modal select {
    font-size: 16px !important;
    padding: 14px 16px !important;
    padding-right: 44px !important;
    border-radius: 8px !important;
    min-height: 48px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Dropdown Arrow Fixes */
  .dropdown-arrow {
    width: 18px !important;
    height: 18px !important;
    flex-shrink: 0 !important;
  }

  .dropdown-arrow img {
    width: 14px !important;
    height: 14px !important;
  }

  /* Placeholder Text */
  .currency-placeholder,
  .wallet-placeholder {
    font-size: 15px !important;
  }

  .placeholder-text {
    font-size: 15px !important;
  }

  /* Option Content */
  .option-content {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .option-name,
  .option-label,
  .wallet-label {
    font-size: 15px !important;
    line-height: 1.3 !important;
  }

  .option-symbol,
  .option-amount,
  .wallet-amount {
    font-size: 13px !important;
    line-height: 1.3 !important;
  }

  .option-network {
    font-size: 12px !important;
    line-height: 1.3 !important;
  }

  /* Check Icons */
  .option-check {
    width: 18px !important;
    height: 18px !important;
    flex-shrink: 0 !important;
  }

  /* Scrollbar for mobile dropdowns */
  .currency-dropdown-menu::-webkit-scrollbar,
  .wallet-dropdown-menu::-webkit-scrollbar,
  .dropdown-menu::-webkit-scrollbar {
    width: 4px !important;
  }

  .currency-dropdown-menu::-webkit-scrollbar-track,
  .wallet-dropdown-menu::-webkit-scrollbar-track,
  .dropdown-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
  }

  .currency-dropdown-menu::-webkit-scrollbar-thumb,
  .wallet-dropdown-menu::-webkit-scrollbar-thumb,
  .dropdown-menu::-webkit-scrollbar-thumb {
    background: rgba(191, 65, 41, 0.6) !important;
    border-radius: 2px !important;
  }

  /* Z-index fixes for mobile */
  .currency-dropdown-container,
  .wallet-dropdown-container {
    z-index: 1000 !important;
  }

  .currency-dropdown-menu,
  .wallet-dropdown-menu {
    z-index: 9999 !important;
  }

  /* Touch improvements */
  .currency-dropdown-trigger,
  .wallet-dropdown-trigger,
  .dropdown-trigger {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }

  .currency-dropdown-option,
  .wallet-dropdown-option,
  .dropdown-item {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .currency-dropdown-trigger,
  .wallet-dropdown-trigger {
    padding: 12px 16px !important;
    font-size: 14px !important;
    min-height: 44px !important;
  }

  .currency-dropdown-option,
  .wallet-dropdown-option {
    padding: 12px 16px !important;
    font-size: 14px !important;
  }

  .currency-icon,
  .option-icon,
  .trigger-icon {
    width: 20px !important;
    height: 20px !important;
    padding: 2px !important;
  }

  .currency-icon img,
  .option-icon img,
  .trigger-icon img {
    width: 14px !important;
    height: 14px !important;
  }

  .option-name,
  .option-label,
  .wallet-label {
    font-size: 14px !important;
  }

  .option-symbol,
  .option-amount,
  .wallet-amount {
    font-size: 12px !important;
  }

  .option-network {
    font-size: 11px !important;
  }
}
