.comparison-table {
  background: var(--bg-primary);
  padding: 80px 0;
}

.comparison-header {
  text-align: center;
  margin-bottom: 60px;
}

.comparison-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.comparison-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
}

.comparison-content {
  max-width: 800px;
  margin: 0 auto 60px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Headers */
.comparison-header-left,
.comparison-header-right {
  padding: 24px;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
}

.comparison-header-left {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.comparison-header-right {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
}

/* Feature Items */
.feature-item {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.feature-item:last-of-type {
  border-bottom: none;
}

.feature-item.traditional {
  background: var(--bg-card);
  color: var(--text-primary);
  border-right: 1px solid var(--border-color);
}

.feature-item.esvc {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

/* Toggle Switch */
.comparison-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.toggle-option {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.toggle-option.active {
  color: var(--accent-orange);
  font-weight: 600;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-orange);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Single Column View */
.comparison-grid.traditional,
.comparison-grid.esvc {
  grid-template-columns: 1fr;
}

.comparison-header-single {
  padding: 24px;
  text-align: center;
  border-radius: 12px 12px 0 0;
  font-size: 18px;
  font-weight: 600;
}

.comparison-header-single.traditional {
  background: var(--bg-card);
  color: var(--text-primary);
}

.comparison-header-single.esvc {
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  color: white;
}

.feature-item.single {
  border-right: none;
  text-align: center;
  padding: 20px 24px;
}

/* Action Buttons */
.comparison-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
}

.comparison-actions .btn-primary,
.comparison-actions .btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  padding: 16px 32px;
}

/* Responsive */
@media (max-width: 768px) {
  .comparison {
    padding: 60px 0 !important;
  }

  .comparison-header {
    margin-bottom: 40px !important;
    padding: 0 20px !important;
  }

  .comparison-title {
    font-size: 32px !important;
    margin-bottom: 16px !important;
    line-height: 1.2 !important;
  }

  .comparison-subtitle {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  .comparison-content {
    padding: 0 20px !important;
  }

  .comparison-grid {
    grid-template-columns: 1fr !important;
    gap: 0 !important;
    border-radius: 12px !important;
    overflow: hidden !important;
  }

  .comparison-header-left {
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 20px !important;
    text-align: center !important;
  }

  .comparison-header-right {
    padding: 20px !important;
    text-align: center !important;
  }

  .feature-item.traditional {
    border-right: none !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 16px 20px !important;
  }

  .feature-item.esvc {
    padding: 16px 20px !important;
  }

  .feature-icon {
    font-size: 18px !important;
  }

  .feature-text {
    font-size: 14px !important;
  }

  .comparison-actions {
    flex-direction: column !important;
    align-items: center !important;
    padding: 0 20px !important;
    gap: 16px !important;
    margin-top: 40px !important;
  }

  .comparison-actions .btn-primary,
  .comparison-actions .btn-secondary {
    width: 100% !important;
    max-width: 320px !important;
    justify-content: center !important;
    padding: 16px 24px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }
}
